# Simple Workflow Implementation Prompt
## AI Agent Task: Implement Streamlined Intelligent Codebase Analysis System

### Objective
Create a simplified, fast, and efficient version of the intelligent codebase analysis system that provides essential customization capabilities with minimal complexity. The system must work across all project lifecycle stages while maintaining sub-30-second setup times.

## Minimal Implementation Path

### 1. Essential Analysis Engine

#### Quick Project Stage Detection
```javascript
// File: simple-analyze/stage-identifier.js
function detectProjectStage(projectPath) {
  const files = getFileList(projectPath, { maxDepth: 2, maxFiles: 100 });
  
  // Fast stage detection using file patterns
  const indicators = {
    idea: files.some(f => /^(README|requirements|project-|spec-)/i.test(f)) && 
          !files.some(f => /\.(js|py|go|rs)$/.test(f)),
    early: files.some(f => /(package\.json|requirements\.txt|go\.mod|Cargo\.toml)$/.test(f)) &&
           files.filter(f => /\.(js|py|go|rs)$/.test(f)).length < 10,
    active: files.filter(f => /\.(js|py|go|rs)$/.test(f)).length >= 10 &&
            !files.some(f => /(docker|deploy|prod)/i.test(f)),
    mature: files.some(f => /(docker|deploy|prod|\.env\.prod)/i.test(f))
  };
  
  return Object.keys(indicators).find(stage => indicators[stage]) || 'active';
}
```

#### Fast Tech Stack Detection
```javascript
// File: simple-analyze/quick-detector.js
function quickDetectTechStack(projectPath) {
  const packageFiles = findPackageFiles(projectPath);
  const analysis = { language: 'unknown', framework: 'none', confidence: 0 };
  
  // Priority detection for common stacks
  if (packageFiles['package.json']) {
    const pkg = JSON.parse(readFile(packageFiles['package.json']));
    analysis.language = 'javascript';
    analysis.framework = detectJSFramework(pkg.dependencies);
    analysis.confidence = 0.9;
  } else if (packageFiles['requirements.txt'] || packageFiles['pyproject.toml']) {
    analysis.language = 'python';
    analysis.framework = detectPythonFramework(projectPath);
    analysis.confidence = 0.8;
  } else if (packageFiles['go.mod']) {
    analysis.language = 'go';
    analysis.framework = detectGoFramework(projectPath);
    analysis.confidence = 0.8;
  }
  
  return analysis;
}

function detectJSFramework(dependencies = {}) {
  const deps = Object.keys(dependencies);
  if (deps.includes('react')) return 'react';
  if (deps.includes('vue')) return 'vue';
  if (deps.includes('express')) return 'express';
  if (deps.includes('next')) return 'nextjs';
  return 'vanilla';
}
```

#### Essential Pattern Recognition
```javascript
// File: simple-analyze/essential-patterns.js
function extractEssentialPatterns(projectPath, techStack) {
  const patterns = {
    fileNaming: 'unknown',
    componentStyle: 'unknown',
    importStyle: 'unknown'
  };
  
  // Sample a few files for pattern detection
  const sampleFiles = getSampleFiles(projectPath, techStack.language, 5);
  
  sampleFiles.forEach(file => {
    const content = readFile(file);
    
    // Quick pattern checks
    if (techStack.language === 'javascript') {
      patterns.fileNaming = detectNamingPattern(file);
      patterns.componentStyle = detectComponentStyle(content);
      patterns.importStyle = detectImportStyle(content);
    }
  });
  
  return patterns;
}
```

### 2. Simplified Document Generation

#### Essential Template Processor
```javascript
// File: simple-customize/template-adapter.js
class SimpleTemplateAdapter {
  constructor() {
    this.templates = loadSimpleTemplates();
  }
  
  generateEssentialDocs(analysis) {
    const docs = {};
    
    // Always generate these core documents
    docs['CLAUDE.md'] = this.processTemplate('claude', analysis);
    docs['README.md'] = this.processTemplate('readme', analysis);
    docs['CONTRIBUTING.md'] = this.processTemplate('contributing', analysis);
    
    // Stage-specific documents
    switch (analysis.stage) {
      case 'idea':
        docs['PROJECT_PLAN.md'] = this.processTemplate('project-plan', analysis);
        break;
      case 'mature':
        docs['MAINTENANCE.md'] = this.processTemplate('maintenance', analysis);
        break;
    }
    
    return docs;
  }
  
  processTemplate(templateName, analysis) {
    const template = this.templates[templateName];
    return template
      .replace(/\{\{PROJECT_NAME\}\}/g, analysis.projectName || 'Project')
      .replace(/\{\{LANGUAGE\}\}/g, analysis.language)
      .replace(/\{\{FRAMEWORK\}\}/g, analysis.framework)
      .replace(/\{\{STAGE\}\}/g, analysis.stage);
  }
}
```

#### Stage-Specific Content Generation
```javascript
// File: simple-customize/doc-generator.js
function generateStageSpecificContent(analysis) {
  const content = {};
  
  switch (analysis.stage) {
    case 'idea':
      content.nextSteps = generateIdeaNextSteps(analysis);
      content.structure = generateRecommendedStructure(analysis);
      break;
      
    case 'early':
      content.patterns = generateDetectedPatterns(analysis);
      content.standards = generateBasicStandards(analysis);
      break;
      
    case 'active':
      content.conventions = generateExtractedConventions(analysis);
      content.workflows = generateDevelopmentWorkflows(analysis);
      break;
      
    case 'mature':
      content.maintenance = generateMaintenanceGuide(analysis);
      content.onboarding = generateOnboardingChecklist(analysis);
      break;
  }
  
  return content;
}

function generateIdeaNextSteps(analysis) {
  const steps = [
    `Set up ${analysis.language} development environment`,
    `Initialize ${analysis.framework} project structure`,
    'Create basic folder organization',
    'Set up version control',
    'Configure basic testing framework'
  ];
  
  return steps.map((step, i) => `${i + 1}. ${step}`).join('\n');
}
```

### 3. Streamlined Installation Integration

#### Modified Simple Installation Script
```bash
#!/bin/bash
# File: SIMPLE-WORKFLOW/install-simple-ai-dev.sh

echo "🚀 Setting up Simple AI Dev Environment..."

# Quick project analysis (5-second timeout)
echo "🔍 Quick project analysis..."
timeout 5s node ~/.agent-os-simple/analyze/quick-analyze.js "$PWD" > .ai-dev-simple/analysis.json

if [ $? -eq 0 ]; then
  PROJECT_STAGE=$(cat .ai-dev-simple/analysis.json | jq -r '.stage')
  TECH_STACK=$(cat .ai-dev-simple/analysis.json | jq -r '.framework')
  echo "📊 Detected: $TECH_STACK project in $PROJECT_STAGE stage"
else
  echo "⚠️  Analysis timeout - using generic setup"
  echo '{"stage":"unknown","language":"unknown","framework":"none"}' > .ai-dev-simple/analysis.json
fi

# Generate essential documentation
echo "📝 Generating essential documentation..."
node ~/.agent-os-simple/customize/generate-docs.js "$PWD"

# Quick validation
echo "✅ Validating setup..."
node ~/.agent-os-simple/validate/quick-check.js "$PWD"

echo "🎉 Simple AI Dev Environment ready!"
echo "💡 Run 'ai-dev enhance' for advanced features"
```

#### Quick Analysis Main Script
```javascript
// File: ~/.agent-os-simple/analyze/quick-analyze.js
const fs = require('fs');
const path = require('path');

function quickAnalyze(projectPath) {
  const startTime = Date.now();
  
  try {
    // Stage detection (fast)
    const stage = detectProjectStage(projectPath);
    
    // Tech stack detection (essential only)
    const techStack = quickDetectTechStack(projectPath);
    
    // Basic pattern recognition
    const patterns = extractEssentialPatterns(projectPath, techStack);
    
    const analysis = {
      timestamp: new Date().toISOString(),
      duration: Date.now() - startTime,
      projectName: path.basename(projectPath),
      stage,
      ...techStack,
      patterns,
      confidence: techStack.confidence
    };
    
    return analysis;
  } catch (error) {
    return {
      error: error.message,
      stage: 'unknown',
      language: 'unknown',
      framework: 'none',
      confidence: 0
    };
  }
}

// CLI execution
if (require.main === module) {
  const projectPath = process.argv[2] || process.cwd();
  const analysis = quickAnalyze(projectPath);
  console.log(JSON.stringify(analysis, null, 2));
}

module.exports = { quickAnalyze };
```

### 4. Essential Template System

#### Core Template Structure
```
simple-templates/
├── claude.template.md         # Basic Claude context
├── readme.template.md         # Essential README
├── contributing.template.md   # Basic contribution guide
├── project-plan.template.md   # For idea stage
└── maintenance.template.md    # For mature stage
```

#### Claude Context Template
```markdown
<!-- File: simple-templates/claude.template.md -->
# {{PROJECT_NAME}} - Claude Context

## Project Overview
- **Language**: {{LANGUAGE}}
- **Framework**: {{FRAMEWORK}}
- **Stage**: {{STAGE}}

## Development Context
{{#if_stage_idea}}
This project is in the planning stage. Focus on:
- Architecture decisions
- Technology validation
- Implementation planning
{{/if_stage_idea}}

{{#if_stage_active}}
This is an active {{FRAMEWORK}} project. Key patterns:
- Follow existing code conventions
- Maintain consistency with established patterns
- Update tests for any changes
{{/if_stage_active}}

## Quick Commands
- `ai-dev analyze` - Re-analyze project
- `ai-dev enhance` - Upgrade to full workflow
- `ai-dev docs` - Generate additional documentation
```

### 5. Rapid Testing Protocol

#### Essential Test Suite
```javascript
// File: test/simple-workflow.test.js
describe('Simple Workflow', () => {
  test('completes setup in under 30 seconds', async () => {
    const startTime = Date.now();
    const result = await runSimpleSetup(testProject);
    const duration = Date.now() - startTime;
    
    expect(duration).toBeLessThan(30000);
    expect(result.success).toBe(true);
  });
  
  test('detects common frameworks correctly', async () => {
    const reactProject = createTestProject('react');
    const analysis = await quickAnalyze(reactProject);
    
    expect(analysis.language).toBe('javascript');
    expect(analysis.framework).toBe('react');
    expect(analysis.confidence).toBeGreaterThan(0.8);
  });
  
  test('handles all project stages', async () => {
    const stages = ['idea', 'early', 'active', 'mature'];
    
    for (const stage of stages) {
      const project = createTestProject(stage);
      const analysis = await quickAnalyze(project);
      expect(analysis.stage).toBe(stage);
    }
  });
  
  test('gracefully handles analysis failures', async () => {
    const corruptProject = createCorruptTestProject();
    const analysis = await quickAnalyze(corruptProject);
    
    expect(analysis.error).toBeDefined();
    expect(analysis.stage).toBe('unknown');
    expect(analysis.confidence).toBe(0);
  });
});
```

### 6. Targeted Documentation Updates

#### Simple Workflow Integration Points
1. **Create simple-install script**: Standalone installation for simple workflow
2. **Add quick-analyze module**: Fast project analysis (5-second limit)
3. **Create essential templates**: Minimal but complete template set
4. **Add upgrade path**: Migration from simple to master workflow
5. **Update main documentation**: Add simple workflow option

#### File Modifications Required
```bash
# New files to create
SIMPLE-WORKFLOW/
├── install-simple-ai-dev.sh
├── quick-analyze.js
├── generate-docs.js
└── templates/
    ├── claude.template.md
    ├── readme.template.md
    └── contributing.template.md

# Existing files to modify
MASTER-WORKFLOW/install-ai-dev-os.sh  # Add simple workflow option
README.md                             # Document simple workflow
```

#### CLI Integration
```bash
# Add to main CLI
ai-dev init --simple          # Use simple workflow
ai-dev init --full            # Use master workflow (default)
ai-dev enhance                # Upgrade simple to master
ai-dev quick-analyze          # Run quick analysis only
```

## Expected Outcomes

### Performance Targets
- **Setup Time**: Under 30 seconds for any project size
- **Analysis Time**: Under 5 seconds for projects with <1000 files
- **Memory Usage**: Under 100MB during analysis
- **Success Rate**: 95%+ successful setups across common project types

### Quality Standards
- **Detection Accuracy**: 90%+ for top 10 frameworks (React, Express, Django, etc.)
- **Document Quality**: Generated docs immediately useful without editing
- **Error Recovery**: Graceful fallback to generic templates on analysis failure
- **User Experience**: Clear feedback and simple upgrade path

### Upgrade Path Success
- **Seamless Migration**: Simple to master workflow without data loss
- **Preserved Customizations**: User modifications maintained during upgrade
- **Enhanced Analysis**: Master workflow builds on simple analysis results
- **Backward Compatibility**: Simple workflow continues to work independently

## Implementation Timeline

### Week 1: Core Analysis
- Quick tech stack detection
- Essential pattern recognition
- Basic stage identification
- Performance optimization

### Week 2: Document Generation
- Essential template system
- Stage-specific content generation
- Basic validation and error handling
- CLI integration

### Week 3: Integration & Testing
- Installation script integration
- Cross-platform testing
- Performance validation
- Error scenario testing

### Week 4: Polish & Documentation
- User experience refinement
- Documentation completion
- Upgrade path implementation
- Final performance optimization

This streamlined implementation provides immediate value while maintaining simplicity and a clear upgrade path to the full Master Workflow system.
